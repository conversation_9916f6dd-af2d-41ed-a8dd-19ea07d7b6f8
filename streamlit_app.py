import streamlit as st
import os
from typing import Optional
import logging

# Import the file processor
try:
    from app.file_processor import create_file_processor
    FILE_PROCESSOR_AVAILABLE = True
except ImportError:
    FILE_PROCESSOR_AVAILABLE = False

# Set up logging
logging.basicConfig(level=logging.INFO)

def main():
    """Main Streamlit application for branded image generation."""
    
    # Set page configuration
    st.set_page_config(
        page_title="Branded Image Generator",
        page_icon="🎨",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Main title
    st.title("🎨 Branded Image Generator")
    st.markdown("Create compelling branded images and content based on your brand guidelines and goals.")
    
    # Sidebar for configuration
    with st.sidebar:
        st.header("Configuration")
        
        # Brand name input
        brand_name = st.text_input(
            "Brand Name",
            placeholder="Enter your brand name (e.g., Nike, Apple, Coca-Cola)",
            help="The name of your brand for which you want to generate content"
        )
        
        # Goal input
        goal = st.text_area(
            "Goal",
            placeholder="Describe your content goal (e.g., 'Create a LinkedIn carousel about sustainability initiatives')",
            height=100,
            help="Describe what you want to achieve with the generated content"
        )
    
    # Main content area
    st.header("📝 Content Input")

    # Content input method selection
    input_method = st.radio(
        "Choose input method:",
        ["Text Input", "File Upload"],
        help="Select how you want to provide content for your branded images"
    )

    content_text = None
    uploaded_file = None

    if input_method == "Text Input":
        content_text = st.text_area(
            "Content Text",
            placeholder="Enter your content here...\n\nThis could be:\n- Article text\n- Product descriptions\n- Marketing copy\n- Research findings\n- Any text you want to transform into branded content",
            height=300,
            help="Provide the textual content you want to use for generating branded images"
        )

    else:  # File Upload
        if not FILE_PROCESSOR_AVAILABLE:
            st.error("❌ File processing is not available. MarkItDown library is not installed.")
            st.info("To enable file processing, install with: `pip install 'markitdown[all]'`")
            uploaded_file = None
        else:
            # Get supported file types from processor
            try:
                processor = create_file_processor()
                supported_types = [ext.lstrip('.') for ext in processor.get_supported_file_types()]
            except Exception as e:
                st.error(f"❌ Error initializing file processor: {str(e)}")
                supported_types = ['txt', 'md', 'pdf', 'docx']

            uploaded_file = st.file_uploader(
                "Upload Content File",
                type=supported_types,
                help=f"Upload a file containing the content you want to use. Supported formats: {', '.join(supported_types[:10])}{'...' if len(supported_types) > 10 else ''}"
            )

            if uploaded_file is not None:
                st.success(f"✅ File uploaded: {uploaded_file.name}")

                # Process the file
                with st.spinner("🔄 Converting file to markdown..."):
                    try:
                        processor = create_file_processor()
                        markdown_content, filename = processor.process_uploaded_file(uploaded_file)

                        # Store the processed content in session state
                        st.session_state['processed_file_content'] = markdown_content
                        st.session_state['processed_file_name'] = filename

                        st.success(f"✅ Successfully processed {filename}")

                        # Show preview of processed content
                        with st.expander("📄 Preview Processed Content", expanded=False):
                            preview_length = 500
                            if len(markdown_content) > preview_length:
                                st.markdown(f"```markdown\n{markdown_content[:preview_length]}...\n```")
                                st.info(f"Showing first {preview_length} characters. Full content will be used for generation.")
                            else:
                                st.markdown(f"```markdown\n{markdown_content}\n```")

                    except Exception as e:
                        st.error(f"❌ Error processing file: {str(e)}")
                        uploaded_file = None
    
    # Generation section
    st.header("🚀 Generate Content")
    
    # Validation
    # Check if we have processed file content in session state
    has_processed_file = 'processed_file_content' in st.session_state and st.session_state['processed_file_content']
    can_generate = bool(brand_name and goal and (content_text or has_processed_file))

    if not can_generate:
        missing_fields = []
        if not brand_name:
            missing_fields.append("Brand Name")
        if not goal:
            missing_fields.append("Goal")
        if not content_text and not has_processed_file:
            missing_fields.append("Content (text or processed file)")

        st.warning(f"Please provide the following required fields: {', '.join(missing_fields)}")
    
    # Generate button
    generate_button = st.button(
        "🎨 Generate Branded Content",
        disabled=not can_generate,
        help="Click to generate branded images based on your inputs"
    )
    
    if generate_button:
        if can_generate:
            # Show processing message
            with st.spinner("🔄 Generating your branded content..."):
                st.info("🚧 Backend processing will be implemented soon!")
                
                # Display the inputs for now
                st.subheader("📋 Your Inputs:")
                
                col1, col2 = st.columns([1, 1])
                
                with col1:
                    st.write("**Brand Name:**", brand_name)
                    st.write("**Goal:**", goal)

                with col2:
                    if content_text:
                        st.write("**Content Type:** Text Input")
                        with st.expander("View Content Text"):
                            st.text(content_text[:500] + "..." if len(content_text) > 500 else content_text)
                    elif has_processed_file:
                        st.write("**Content Type:** Processed File")
                        st.write("**File Name:**", st.session_state.get('processed_file_name', 'Unknown'))
                        processed_content = st.session_state['processed_file_content']
                        st.write("**Content Length:**", f"{len(processed_content)} characters")
                        with st.expander("View Processed Content"):
                            st.markdown(processed_content[:500] + "..." if len(processed_content) > 500 else processed_content)
                
                # Placeholder for results
                st.success("✅ Content generation request processed!")
                st.info("🔮 Generated images and content will appear here once the backend is connected.")
    
    # Footer
    st.markdown("---")
    st.markdown(
        """
        <div style='text-align: center; color: #666;'>
            <p>Branded Image Generator - Transform your content into compelling branded visuals</p>
        </div>
        """,
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()
