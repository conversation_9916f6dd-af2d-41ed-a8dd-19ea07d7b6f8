import streamlit as st
import os
from typing import Optional
import logging

# Import the backend service
try:
    from app.backend_service import get_backend_service
    BACKEND_AVAILABLE = True
except ImportError:
    BACKEND_AVAILABLE = False

# Set up logging
logging.basicConfig(level=logging.INFO)

def main():
    """Main Streamlit application for branded image generation."""
    
    # Set page configuration
    st.set_page_config(
        page_title="Branded Image Generator",
        page_icon="🎨",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Main title
    st.title("🎨 Branded Image Generator")
    st.markdown("Create compelling branded images and content based on your brand guidelines and goals.")
    
    # Sidebar for configuration
    with st.sidebar:
        st.header("Configuration")
        
        # Brand name input
        brand_name = st.text_input(
            "Brand Name",
            placeholder="Enter your brand name (e.g., Nike, Apple, Coca-Cola)",
            help="The name of your brand for which you want to generate content"
        )
        
        # Goal input
        goal = st.text_area(
            "Goal",
            placeholder="Describe your content goal (e.g., 'Create a LinkedIn carousel about sustainability initiatives')",
            height=100,
            help="Describe what you want to achieve with the generated content"
        )
    
    # Main content area
    st.header("📝 Content Input")

    # Content input method selection
    input_method = st.radio(
        "Choose input method:",
        ["Text Input", "File Upload"],
        help="Select how you want to provide content for your branded images"
    )

    content_text = None
    uploaded_file = None

    if input_method == "Text Input":
        content_text = st.text_area(
            "Content Text",
            placeholder="Enter your content here...\n\nThis could be:\n- Article text\n- Product descriptions\n- Marketing copy\n- Research findings\n- Any text you want to transform into branded content",
            height=300,
            help="Provide the textual content you want to use for generating branded images"
        )

    else:  # File Upload
        if not BACKEND_AVAILABLE:
            st.error("❌ Backend service is not available.")
            st.info("Please check that all dependencies are installed.")
            uploaded_file = None
        else:
            # Get backend service and check file processing availability
            try:
                backend = get_backend_service()
                if not backend.is_file_processing_available():
                    st.error("❌ File processing is not available. MarkItDown library is not installed.")
                    st.info("To enable file processing, install with: `pip install 'markitdown[all]'`")
                    uploaded_file = None
                else:
                    supported_types = [ext.lstrip('.') for ext in backend.get_supported_file_types()]

                    uploaded_file = st.file_uploader(
                        "Upload Content File",
                        type=supported_types,
                        help=f"Upload a file containing the content you want to use. Supported formats: {', '.join(supported_types[:10])}{'...' if len(supported_types) > 10 else ''}"
                    )

                    if uploaded_file is not None:
                        st.success(f"✅ File uploaded: {uploaded_file.name}")

                        # Store the uploaded file in session state for processing
                        st.session_state['uploaded_file'] = uploaded_file
                        st.session_state['uploaded_file_name'] = uploaded_file.name

                        st.info("📋 File will be processed when you click 'Generate Branded Content'")

            except Exception as e:
                st.error(f"❌ Error initializing backend service: {str(e)}")
                uploaded_file = None
    
    # Generation section
    st.header("🚀 Generate Content")
    
    # Validation
    # Check if we have uploaded file in session state
    has_uploaded_file = 'uploaded_file' in st.session_state and st.session_state['uploaded_file'] is not None
    can_generate = bool(brand_name and goal and (content_text or has_uploaded_file))

    if not can_generate:
        missing_fields = []
        if not brand_name:
            missing_fields.append("Brand Name")
        if not goal:
            missing_fields.append("Goal")
        if not content_text and not has_uploaded_file:
            missing_fields.append("Content (text or uploaded file)")

        st.warning(f"Please provide the following required fields: {', '.join(missing_fields)}")
    
    # Generate button
    generate_button = st.button(
        "🎨 Generate Branded Content",
        disabled=not can_generate,
        help="Click to generate branded images based on your inputs"
    )
    
    if generate_button:
        if can_generate:
            # Show processing message
            with st.spinner("🔄 Generating your branded content..."):
                try:
                    # Get backend service
                    backend = get_backend_service()

                    # Prepare inputs
                    uploaded_file_obj = st.session_state.get('uploaded_file') if has_uploaded_file else None

                    # Process the request
                    result = backend.process_user_request(
                        brand_name=brand_name,
                        goal=goal,
                        content_text=content_text,
                        uploaded_file=uploaded_file_obj,
                        force_refresh_brand=False
                    )

                    if result['success']:
                        st.success("✅ Content generation completed successfully!")

                        # Display results
                        st.subheader("🎨 Generated Content")

                        # Show metadata
                        col1, col2 = st.columns([1, 1])

                        with col1:
                            st.write("**Brand:**", result['brand_name'])
                            st.write("**Goal:**", result['goal'])
                            st.write("**Content:**", result['content_title'])
                            st.write("**Images Generated:**", len(result['images']))

                        with col2:
                            metadata = result['metadata']
                            st.write("**Content Type:**", metadata['content_type'])
                            st.write("**Style:**", metadata['overall_style'])
                            st.write("**Tone:**", metadata['article_tone'])
                            st.write("**Target Audience:**", metadata['target_audience'])

                        # Display generated images
                        if result['images']:
                            st.subheader("🖼️ Generated Images")

                            for i, image in enumerate(result['images'], 1):
                                st.markdown(f"### Image {i}: {image['purpose']}")

                                col1, col2 = st.columns([1, 1])

                                with col1:
                                    if image['local_path'] and os.path.exists(image['local_path']):
                                        st.image(image['local_path'], caption=image['purpose'])
                                    else:
                                        st.info("Image saved locally but not available for preview")

                                with col2:
                                    st.write("**Purpose:**", image['purpose'])
                                    with st.expander("View Prompt"):
                                        st.text(image['prompt'])
                                    if image['local_path']:
                                        st.write("**Saved to:**", image['local_path'])

                        # Show generation summary
                        if result['summary']:
                            with st.expander("📄 Generation Summary", expanded=False):
                                st.text(result['summary'])

                        # Clear uploaded file from session state after successful processing
                        if 'uploaded_file' in st.session_state:
                            del st.session_state['uploaded_file']
                        if 'uploaded_file_name' in st.session_state:
                            del st.session_state['uploaded_file_name']

                    else:
                        st.error(f"❌ Generation failed: {result.get('error', 'Unknown error')}")
                        st.info(result.get('message', 'Please try again or check your inputs.'))

                except Exception as e:
                    st.error(f"❌ An error occurred: {str(e)}")
                    st.info("Please check your inputs and try again.")
    
    # Footer
    st.markdown("---")
    st.markdown(
        """
        <div style='text-align: center; color: #666;'>
            <p>Branded Image Generator - Transform your content into compelling branded visuals</p>
        </div>
        """,
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()
