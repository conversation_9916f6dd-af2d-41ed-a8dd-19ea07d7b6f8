#!/usr/bin/env python3
"""
Simple test for backend service initialization.
"""

import sys
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.append(str(Path(__file__).parent))

def test_backend_initialization():
    """Test that backend service can be initialized."""
    print("Testing backend service initialization...")
    
    try:
        from app.backend_service import get_backend_service
        
        backend = get_backend_service()
        print("✅ Backend service initialized successfully")
        
        print(f"File processing available: {backend.is_file_processing_available()}")
        print(f"Article processor: {backend.article_processor is not None}")
        print(f"Goal processor: {backend.goal_processor is not None}")
        print(f"Image generator: {backend.image_generator is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Backend initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_article_processing():
    """Test article processing component."""
    print("\nTesting article processing...")
    
    try:
        from app.backend_service import get_backend_service
        
        backend = get_backend_service()
        
        # Test simple content processing
        test_content = "This is a test article about sustainable products."
        article_content = backend._process_content_input(test_content, None)
        
        print(f"✅ Article processed: {article_content.title}")
        print(f"Summary: {article_content.summary[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Article processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Simple Backend Tests")
    print("=" * 40)
    
    success1 = test_backend_initialization()
    success2 = test_article_processing()
    
    if success1 and success2:
        print("\n✅ All tests passed!")
    else:
        print("\n❌ Some tests failed!")
