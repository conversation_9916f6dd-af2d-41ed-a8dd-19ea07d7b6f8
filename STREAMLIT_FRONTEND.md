# Streamlit Frontend for Branded Image Generator

This document describes the Streamlit frontend for the branded image generation application.

## Features

The Streamlit frontend provides a user-friendly interface with the following features:

### Input Fields
- **Brand Name**: Text input for specifying the brand
- **Goal**: Text area for describing the content generation goal
- **Content Input**: Two options:
  - **Text Input**: Direct text entry in a text area
  - **File Upload**: Upload files and convert to markdown using Microsoft's MarkItDown library
    - Supported formats: PDF, DOCX, PPTX, XLSX, XLS, TXT, MD, HTML, CSV, JSON, XML, images (JPG, PNG, etc.), audio files (MP3, WAV), EPUB, ZIP, and more
    - Real-time file processing and conversion to markdown
    - Preview of processed content before generation

### Advanced Options
- **Output Format**: Choose between Single Image, LinkedIn Carousel, Instagram Post, Twitter Card
- **Image Style**: Select from Professional, Creative, Minimalist, Bold, Elegant
- **Include Text Overlay**: Toggle for text overlays on images

### Validation
- Form validation ensures all required fields are provided
- Clear feedback on missing fields
- Generate button is disabled until all requirements are met

## Running the Frontend

### Using Docker Compose (Recommended)

1. Start the Streamlit frontend:
```bash
docker compose up streamlit-frontend
```

2. Access the application at: http://localhost:8501

### Development Mode

For development, you can run Streamlit directly:

```bash
# Install dependencies
pip install -r requirements.txt

# Run Streamlit
streamlit run streamlit_app.py
```

## File Structure

```
streamlit_app.py          # Main Streamlit application
uploads/                  # Directory for uploaded files
docker-compose.yml        # Updated with streamlit-frontend service
Dockerfile               # Updated to expose port 8501
requirements.txt         # Updated with streamlit dependency
```

## Implemented Features

### ✅ File Processing with MarkItDown
- **Microsoft MarkItDown Integration**: Uses Microsoft's MarkItDown library for file conversion
- **Wide Format Support**: Supports 20+ file formats including PDF, Office documents, images, audio, and more
- **Real-time Processing**: Files are converted to markdown immediately upon upload
- **Content Preview**: Users can preview the processed markdown content
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Automatic Cleanup**: Temporary files are automatically cleaned up after processing

### 📁 File Processing Module (`app/file_processor.py`)
- `FileProcessor` class with MarkItDown integration
- Support for all MarkItDown-compatible file types
- File type validation and error handling
- Session state management for processed content
- Temporary file handling with automatic cleanup

## Next Steps

1. **Backend Integration**: Connect the frontend to the existing brand research and image generation backend
2. **Real-time Generation**: Add actual image generation functionality using processed file content
3. **Results Display**: Show generated images and content in the interface
4. **Download Options**: Allow users to download generated content
5. **Progress Tracking**: Add progress bars and status updates during generation
6. **Enhanced File Support**: Add support for additional file types as MarkItDown evolves

## Technical Notes

- The frontend is containerized and runs on port 8501
- File uploads are saved to the `/app/uploads` directory in the container
- The interface is responsive and works well on different screen sizes
- All existing volumes are mounted to allow access to brands, screenshots, and generated content
