# Streamlit Frontend for Branded Image Generator

This document describes the Streamlit frontend for the branded image generation application.

## Features

The Streamlit frontend provides a user-friendly interface with the following features:

### Input Fields
- **Brand Name**: Text input for specifying the brand
- **Goal**: Text area for describing the content generation goal
- **Content Input**: Two options:
  - **Text Input**: Direct text entry in a text area
  - **File Upload**: Upload files and convert to markdown using Microsoft's MarkItDown library
    - Supported formats: PDF, DOCX, PPTX, XLSX, XLS, TXT, MD, HTML, CSV, JSON, XML, images (JPG, PNG, etc.), audio files (MP3, WAV), EPUB, ZIP, and more
    - Real-time file processing and conversion to markdown
    - Preview of processed content before generation

### Advanced Options
- **Output Format**: Choose between Single Image, LinkedIn Carousel, Instagram Post, Twitter Card
- **Image Style**: Select from Professional, Creative, Minimalist, Bold, Elegant
- **Include Text Overlay**: Toggle for text overlays on images

### Validation
- Form validation ensures all required fields are provided
- Clear feedback on missing fields
- Generate button is disabled until all requirements are met

## Running the Frontend

### Using Docker Compose (Recommended)

1. Start the Streamlit frontend:
```bash
docker compose up streamlit-frontend
```

2. Access the application at: http://localhost:8501

### Development Mode

For development, you can run Streamlit directly:

```bash
# Install dependencies
pip install -r requirements.txt

# Run Streamlit
streamlit run streamlit_app.py
```

## File Structure

```
streamlit_app.py          # Main Streamlit application
uploads/                  # Directory for uploaded files
docker-compose.yml        # Updated with streamlit-frontend service
Dockerfile               # Updated to expose port 8501
requirements.txt         # Updated with streamlit dependency
```

## Implemented Features

### ✅ File Processing with MarkItDown
- **Microsoft MarkItDown Integration**: Uses Microsoft's MarkItDown library for file conversion
- **Wide Format Support**: Supports 20+ file formats including PDF, Office documents, images, audio, and more
- **Real-time Processing**: Files are converted to markdown immediately upon upload
- **Content Preview**: Users can preview the processed markdown content
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Automatic Cleanup**: Temporary files are automatically cleaned up after processing

### 📁 File Processing Module (`app/file_processor.py`)
- `FileProcessor` class with MarkItDown integration
- Support for all MarkItDown-compatible file types
- File type validation and error handling
- Session state management for processed content
- Temporary file handling with automatic cleanup

## ✅ Backend Integration Complete

### 🔗 Backend Service (`app/backend_service.py`)
- **Complete Workflow Integration**: Connects all backend components seamlessly
- **File Processing**: Integrates MarkItDown file processing with article processing
- **Brand Research**: Automatic brand profile research and caching
- **Goal Processing**: AI-powered goal interpretation with article context
- **Image Generation**: Full image generation pipeline with OpenAI integration
- **Error Handling**: Comprehensive error handling and user feedback

### 🎯 Complete User Workflow
1. **Input**: User provides brand name, goal, and content (text or file)
2. **File Processing**: Files are converted to markdown using MarkItDown
3. **Content Analysis**: Article processor extracts key themes, tone, and structure
4. **Brand Research**: Brand profile is researched or loaded from cache
5. **Goal Processing**: AI interprets the goal with article context
6. **Image Generation**: Multiple branded images are generated
7. **Results Display**: Images, metadata, and summary are shown to user

### 🖼️ Image Generation Features
- **Multiple Images**: Generates 1-10 images based on goal requirements
- **Purpose-Specific**: Each image has a specific purpose (title slide, content slide, etc.)
- **Brand-Aligned**: Images follow brand guidelines and visual identity
- **Content-Aware**: Images incorporate article themes and messaging
- **Local Storage**: Images are saved locally with organized file structure

### 📊 Results Display
- **Image Gallery**: Visual display of all generated images
- **Metadata**: Content type, style, tone, and target audience information
- **Generation Summary**: Detailed summary of the generation process
- **File Paths**: Local paths for accessing generated images

## Next Steps

1. **Testing**: Test the complete workflow with various brands and content types
2. **Performance Optimization**: Optimize image generation speed and quality
3. **Download Options**: Add bulk download functionality for generated content
4. **Advanced Features**: Add image editing and customization options
5. **Analytics**: Track usage patterns and generation success rates
6. **Enhanced UI**: Improve user interface with better progress indicators

## Technical Notes

- The frontend is containerized and runs on port 8501
- File uploads are saved to the `/app/uploads` directory in the container
- The interface is responsive and works well on different screen sizes
- All existing volumes are mounted to allow access to brands, screenshots, and generated content
