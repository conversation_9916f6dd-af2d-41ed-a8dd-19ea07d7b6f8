# Streamlit Frontend for Branded Image Generator

This document describes the Streamlit frontend for the branded image generation application.

## Features

The Streamlit frontend provides a user-friendly interface with the following features:

### Input Fields
- **Brand Name**: Text input for specifying the brand
- **Goal**: Text area for describing the content generation goal
- **Content Input**: Two options:
  - **Text Input**: Direct text entry in a text area
  - **File Upload**: Upload files (TXT, MD, PDF, DOCX) - backend processing to be implemented

### Advanced Options
- **Output Format**: Choose between Single Image, LinkedIn Carousel, Instagram Post, Twitter Card
- **Image Style**: Select from Professional, Creative, Minimalist, Bold, Elegant
- **Include Text Overlay**: Toggle for text overlays on images

### Validation
- Form validation ensures all required fields are provided
- Clear feedback on missing fields
- Generate button is disabled until all requirements are met

## Running the Frontend

### Using Docker Compose (Recommended)

1. Start the Streamlit frontend:
```bash
docker compose up streamlit-frontend
```

2. Access the application at: http://localhost:8501

### Development Mode

For development, you can run Streamlit directly:

```bash
# Install dependencies
pip install -r requirements.txt

# Run Streamlit
streamlit run streamlit_app.py
```

## File Structure

```
streamlit_app.py          # Main Streamlit application
uploads/                  # Directory for uploaded files
docker-compose.yml        # Updated with streamlit-frontend service
Dockerfile               # Updated to expose port 8501
requirements.txt         # Updated with streamlit dependency
```

## Next Steps

1. **Backend Integration**: Connect the frontend to the existing brand research and image generation backend
2. **File Processing**: Implement file upload processing for PDF, DOCX, and other formats
3. **Real-time Generation**: Add actual image generation functionality
4. **Results Display**: Show generated images and content in the interface
5. **Download Options**: Allow users to download generated content
6. **Progress Tracking**: Add progress bars and status updates during generation

## Technical Notes

- The frontend is containerized and runs on port 8501
- File uploads are saved to the `/app/uploads` directory in the container
- The interface is responsive and works well on different screen sizes
- All existing volumes are mounted to allow access to brands, screenshots, and generated content
