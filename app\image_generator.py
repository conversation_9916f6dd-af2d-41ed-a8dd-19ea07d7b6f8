"""
Dynamic image generation pipeline that combines brand profile, goal requirements,
and article content to generate on-brand images.
"""

import os
import json
import base64
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from .openai_client import OpenAIClient
from .goal_processor import GoalRequirements, ImageRequirement
from .article_processor import ArticleContent


@dataclass
class GeneratedImage:
    """Represents a generated image with metadata."""
    purpose: str
    prompt: str
    image_url: str
    local_path: Optional[str] = None
    metadata: Optional[Dict] = None


@dataclass
class ImageGenerationResult:
    """Result of image generation process."""
    brand_name: str
    goal_description: str
    article_title: str
    images: List[GeneratedImage]
    generation_summary: str
    success: bool
    error_message: Optional[str] = None


class ImageGenerator:
    """
    Generates on-brand images based on brand profile, goal requirements, and article content.
    """
    
    def __init__(self):
        self.openai_client = OpenAIClient()
    
    def generate_images(
        self, 
        brand_profile: Dict, 
        goal_requirements: GoalRequirements, 
        article_content: ArticleContent
    ) -> ImageGenerationResult:
        """
        Generate images based on brand profile, goal, and article content.
        
        Args:
            brand_profile: Complete brand profile dictionary
            goal_requirements: Processed goal requirements
            article_content: Processed article content
            
        Returns:
            ImageGenerationResult with generated images and metadata
        """
        brand_name = brand_profile.get('metadata', {}).get('brand_name', 'Unknown Brand')
        
        print(f"Generating images for {brand_name}")
        print(f"Goal: {goal_requirements.goal_description}")
        print(f"Article: {article_content.title}")
        print(f"Images to generate: {goal_requirements.image_count}")
        
        try:
            generated_images = []
            
            for i, image_req in enumerate(goal_requirements.images, 1):
                print(f"Generating image {i}/{len(goal_requirements.images)}: {image_req.purpose}")
                
                # Create image-specific prompt
                prompt = self._create_image_prompt(
                    brand_profile, 
                    goal_requirements, 
                    article_content, 
                    image_req
                )
                
                # Generate image
                print(f"[MAIN_LOOP_DEBUG] About to call _generate_single_image...")
                image_url = self._generate_single_image(prompt)
                print(f"[MAIN_LOOP_DEBUG] Returned from _generate_single_image")
                # print(f"[MAIN_LOOP_DEBUG] image_url value: {image_url}")
                print(f"[MAIN_LOOP_DEBUG] image_url type: {type(image_url)}")
                print(f"[MAIN_LOOP_DEBUG] image_url bool evaluation: {bool(image_url)}")

                if image_url:
                    # Save image locally
                    local_path = self._save_image_locally(
                        image_url, 
                        brand_name, 
                        goal_requirements.goal_description, 
                        image_req.purpose, 
                        i
                    )
                    
                    generated_images.append(GeneratedImage(
                        purpose=image_req.purpose,
                        prompt=prompt,
                        image_url=image_url,
                        local_path=local_path,
                        metadata={
                            'content_focus': image_req.content_focus
                        }
                    ))
                    
                    print(f"✅ Generated: {image_req.purpose}")
                else:
                    print(f"❌ Failed to generate: {image_req.purpose}")
            
            # Create generation summary
            summary = self._create_generation_summary(
                brand_profile, goal_requirements, article_content, generated_images
            )
            
            return ImageGenerationResult(
                brand_name=brand_name,
                goal_description=goal_requirements.goal_description,
                article_title=article_content.title,
                images=generated_images,
                generation_summary=summary,
                success=len(generated_images) > 0
            )
            
        except Exception as e:
            print(f"Error during image generation: {e}")
            return ImageGenerationResult(
                brand_name=brand_name,
                goal_description=goal_requirements.goal_description,
                article_title=article_content.title,
                images=[],
                generation_summary="",
                success=False,
                error_message=str(e)
            )
    
    def _create_image_prompt(
        self, 
        brand_profile: Dict, 
        goal_requirements: GoalRequirements, 
        article_content: ArticleContent, 
        image_req: ImageRequirement
    ) -> str:
        """Create a detailed prompt for image generation."""
        
        # Extract brand information
        metadata = brand_profile.get('metadata', {})
        brand_name = metadata.get('brand_name', 'Brand')
        brand_description = metadata.get('description', '')
        
        # Extract color information
        color_system = brand_profile.get('color_system', {})
        primary_colors = color_system.get('primary', [])
        color_info = ""
        if primary_colors:
            color_names = [color.get('name', '') for color in primary_colors if color.get('name')]
            if color_names:
                color_info = f"Primary brand colors: {', '.join(color_names)}. "
        
        # Extract tone of voice
        tone_of_voice = metadata.get('tone_of_voice', {})
        tone_keywords = tone_of_voice.get('keywords', [])
        tone_info = ""
        if tone_keywords:
            tone_info = f"Brand tone: {', '.join(tone_keywords[:5])}. "
        
        # Extract typography
        typography = brand_profile.get('typography', {})
        font_info = ""
        if typography.get('primary_font', {}).get('family'):
            font_info = f"Primary font: {typography['primary_font']['family']}. "
        
        # Create comprehensive prompt with detailed layout instructions
        prompt = f"""Create a professional image for {brand_name} with the following specifications:

PURPOSE: {image_req.purpose}
CONTENT FOCUS: {image_req.content_focus}

BRAND CONTEXT:
- Brand: {brand_name}
- Description: {brand_description}
- {color_info}
- {tone_info}
- {font_info}

CONTENT CONTEXT:
- Article Title: {image_req.title}
- Main Theme: {', '.join(article_content.main_themes[:3]) if article_content.main_themes else 'General content'}
- Tone: {article_content.tone}
- Target Audience: {article_content.target_audience}

DESIGN REQUIREMENTS:
- Overall Style: {goal_requirements.overall_style}
- Brand Elements: {', '.join(goal_requirements.brand_elements_needed)}
- Design Notes: {image_req.design_notes}

KEY CONTENT POINTS:
{chr(10).join([f"- {point}" for point in article_content.key_points[:5]])}

CRITICAL LAYOUT REQUIREMENTS:
- ENSURE ALL TEXT STAYS COMPLETELY WITHIN IMAGE BOUNDARIES with proper margins
- Leave at least 40-60 pixels margin from all edges for text elements
- Brand name should be positioned in the top area with sufficient top margin
- Main title text should be centered vertically and horizontally with adequate spacing
- Use appropriate font sizes that fit comfortably within the image dimensions
- Test text placement to ensure no text is cut off or extends beyond image edges
- If text is too long, use line breaks or reduce font size to fit within boundaries
- Maintain visual hierarchy while keeping all elements fully visible
- Consider text wrapping for longer titles to prevent overflow

TYPOGRAPHY GUIDELINES:
- Use clean, readable fonts that match the brand's professional tone
- Ensure high contrast between text and background for readability
- Scale text appropriately for the image size and viewing context
- Brand name: smaller, positioned at top with margin
- Main title: larger, centered, with proper line spacing
- Supporting text: medium size, well-spaced

Create a visually appealing, on-brand image that effectively communicates the content while maintaining brand consistency. The image should be professional, engaging, and appropriate for the specified purpose and target audience. MOST IMPORTANTLY: Ensure all text elements are completely visible and properly contained within the image boundaries."""
        print(f"[IMAGE_PROMPT_DEBUG] Created image prompt: {prompt}")
        return prompt
    
    def _generate_single_image(self, prompt: str) -> Optional[str]:
        """Generate a single image using ChatGPT's new gpt-image-1 model."""
        print(f"[IMAGE_GEN_DEBUG] Starting image generation...")
        print(f"[IMAGE_GEN_DEBUG] Prompt length: {len(prompt)} characters")
        print(f"[IMAGE_GEN_DEBUG] Prompt preview: {prompt[:100]}...")

        try:
            # Use the new Responses API with gpt-4.1 model
            print(f"[IMAGE_GEN_DEBUG] Calling generate_image_with_responses_api...")
            data_uri = self.openai_client.generate_image_with_responses_api(prompt)
            print(f"[IMAGE_GEN_DEBUG] Received response: {type(data_uri)}")
            print(f"[IMAGE_GEN_DEBUG] Data URI: {data_uri[:50] if data_uri else 'None'}...")

            if data_uri:
                print(f"[IMAGE_GEN_DEBUG] Data URI type: {type(data_uri)}")
                print(f"[IMAGE_GEN_DEBUG] Data URI length: {len(data_uri)}")
                print(f"[IMAGE_GEN_DEBUG] ✅ Successfully returning data URI")
                return data_uri
            else:
                print(f"[IMAGE_GEN_DEBUG] ❌ No data URI in response")
                return None

        except Exception as e:
            print(f"[IMAGE_GEN_DEBUG] ❌ Exception in Responses API: {type(e).__name__}: {e}")
            print("[IMAGE_GEN_DEBUG] Attempting fallback to standard image generation...")
            try:
                # Fallback to standard method
                urls = self.openai_client.generate_image(prompt, n=1)
                print(f"[IMAGE_GEN_DEBUG] Fallback response: {urls}")
                if urls and len(urls) > 0:
                    print(f"[IMAGE_GEN_DEBUG] ✅ Fallback successful")
                    return urls[0]
                else:
                    print(f"[IMAGE_GEN_DEBUG] ❌ Fallback returned no URLs")
                    return None
            except Exception as fallback_error:
                print(f"[IMAGE_GEN_DEBUG] ❌ Fallback also failed: {fallback_error}")
                return None
    
    def _save_image_locally(
        self,
        image_data: str,
        brand_name: str,
        goal: str,
        purpose: str,
        index: int
    ) -> Optional[str]:
        """Save generated image locally. Handles both URLs and data URIs."""
        try:
            import requests
            import base64

            # Create output directory
            brand_id = brand_name.lower().replace(' ', '_').replace('"', '')
            goal_id = goal.lower().replace(' ', '_')[:20]  # Limit length
            output_dir = f"generated_images/{brand_id}/{goal_id}"
            os.makedirs(output_dir, exist_ok=True)

            # Create filename
            purpose_clean = purpose.lower().replace(' ', '_').replace('/', '_')[:30]
            filename = f"{index:02d}_{purpose_clean}.png"
            filepath = os.path.join(output_dir, filename)

            # Handle data URI (base64 encoded image)
            if image_data.startswith('data:image/'):
                print(f"[SAVE_DEBUG] Handling data URI, length: {len(image_data)}")
                # Extract base64 data from data URI
                header, base64_data = image_data.split(',', 1)
                image_bytes = base64.b64decode(base64_data)

                # Save image
                with open(filepath, 'wb') as f:
                    f.write(image_bytes)

                print(f"Image saved from data URI: {filepath}")
                return filepath

            # Handle regular URL
            else:
                print(f"[SAVE_DEBUG] Handling regular URL: {image_data[:50]}...")
                response = requests.get(image_data)
                response.raise_for_status()

                # Save image
                with open(filepath, 'wb') as f:
                    f.write(response.content)

                print(f"Image saved from URL: {filepath}")
                return filepath

        except Exception as e:
            print(f"Error saving image locally: {e}")
            return None
    
    def _create_generation_summary(
        self, 
        brand_profile: Dict, 
        goal_requirements: GoalRequirements, 
        article_content: ArticleContent, 
        generated_images: List[GeneratedImage]
    ) -> str:
        """Create a summary of the generation process."""
        
        brand_name = brand_profile.get('metadata', {}).get('brand_name', 'Unknown Brand')
        
        summary_parts = [
            f"Image Generation Summary for {brand_name}",
            f"Goal: {goal_requirements.goal_description}",
            f"Article: {article_content.title}",
            f"Generated {len(generated_images)} out of {goal_requirements.image_count} requested images",
            "",
            "Generated Images:"
        ]
        
        for i, img in enumerate(generated_images, 1):
            summary_parts.append(f"{i}. {img.purpose}")
            if img.local_path:
                summary_parts.append(f"   Saved to: {img.local_path}")
        
        if goal_requirements.additional_notes:
            summary_parts.extend(["", f"Additional Notes: {goal_requirements.additional_notes}"])
        
        return "\n".join(summary_parts)


def generate_images(
    brand_profile: Dict, 
    goal_requirements: GoalRequirements, 
    article_content: ArticleContent
) -> ImageGenerationResult:
    """
    Convenience function for image generation.
    
    Args:
        brand_profile: Complete brand profile dictionary
        goal_requirements: Processed goal requirements
        article_content: Processed article content
        
    Returns:
        ImageGenerationResult with generated images and metadata
    """
    generator = ImageGenerator()
    return generator.generate_images(brand_profile, goal_requirements, article_content)
