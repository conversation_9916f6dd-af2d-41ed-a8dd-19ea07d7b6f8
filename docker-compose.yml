version: '3.8'

services:
  brand-agent:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./brands:/app/brands
      - ./screenshots:/app/screenshots
      - ./content_output:/app/content_output
      - ./generated_images:/app/generated_images
      - ./generation_summaries:/app/generation_summaries
    environment:
      # IMPORTANT: Replace 'YOUR_OPENAI_API_KEY' with your actual OpenAI API key
      # Or, better yet, set it in your shell environment before running docker compose
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    # No default command - use 'docker compose run --rm brand-agent <your-command>' to run specific commands

  streamlit-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8501:8501"
    volumes:
      - ./brands:/app/brands
      - ./screenshots:/app/screenshots
      - ./content_output:/app/content_output
      - ./generated_images:/app/generated_images
      - ./generation_summaries:/app/generation_summaries
      - ./uploads:/app/uploads  # For file uploads
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    command: streamlit run streamlit_app.py --server.port=8501 --server.address=0.0.0.0
