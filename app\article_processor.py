"""
Article processing module for extracting and analyzing content
that will be used for image generation.
"""

import os
import re
from typing import Dict, List, Optional, Union
from dataclasses import dataclass

from .openai_client import OpenAIClient


@dataclass
class ArticleContent:
    """Represents processed article content."""
    title: str
    summary: str
    key_points: List[str]
    main_themes: List[str]
    tone: str
    target_audience: str
    call_to_action: Optional[str]
    raw_content: str


class ArticleProcessor:
    """
    Processes article content and extracts key information for image generation.
    """
    
    def __init__(self):
        self.openai_client = OpenAIClient()
    
    def process_content(self, content: Union[str, Dict], content_type: str = "auto") -> ArticleContent:
        """
        Process content and extract key information for image generation.
        
        Args:
            content: Raw content (string) or structured content (dict)
            content_type: Type of content ("article", "campaign", "product", "auto")
            
        Returns:
            ArticleContent object with processed information
        """
        print(f"Processing content (type: {content_type})")
        
        # Handle different content input types
        if isinstance(content, dict):
            raw_content = self._dict_to_text(content)
        elif isinstance(content, str):
            raw_content = content
        else:
            raise ValueError("Content must be string or dictionary")
        
        # Clean and prepare content
        cleaned_content = self._clean_content(raw_content)
        
        if not cleaned_content.strip():
            return self._create_empty_content()
        
        # Use AI to analyze and extract key information
        analysis = self._analyze_content_with_ai(cleaned_content, content_type)
        
        if not analysis:
            return self._create_basic_content(cleaned_content)
        
        return self._create_article_content(analysis, cleaned_content)
    
    def process_file(self, file_path: str, content_type: str = "auto") -> ArticleContent:
        """
        Process content from a file.
        
        Args:
            file_path: Path to the content file
            content_type: Type of content ("article", "campaign", "product", "auto")
            
        Returns:
            ArticleContent object with processed information
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Content file not found: {file_path}")
        
        print(f"Processing content from file: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return self.process_content(content, content_type)
    
    def _dict_to_text(self, content_dict: Dict) -> str:
        """Convert dictionary content to text."""
        text_parts = []
        
        # Common fields that might contain content
        content_fields = ['title', 'headline', 'content', 'body', 'text', 'description', 'summary']
        
        for field in content_fields:
            if field in content_dict and content_dict[field]:
                text_parts.append(str(content_dict[field]))
        
        # If no standard fields found, convert entire dict to text
        if not text_parts:
            for key, value in content_dict.items():
                if isinstance(value, (str, int, float)):
                    text_parts.append(f"{key}: {value}")
        
        return "\n\n".join(text_parts)
    
    def _clean_content(self, content: str) -> str:
        """Clean and normalize content text."""
        # Remove excessive whitespace
        content = re.sub(r'\s+', ' ', content)
        
        # Remove HTML tags if present
        content = re.sub(r'<[^>]+>', '', content)
        
        # Remove URLs
        content = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', content)
        
        # Clean up extra spaces and newlines
        content = re.sub(r'\n\s*\n', '\n\n', content)
        content = content.strip()
        
        return content
    
    def _analyze_content_with_ai(self, content: str, content_type: str) -> Optional[Dict]:
        """Use AI to analyze content and extract key information."""
        
        prompt = f"""Analyze the following content and extract key information for visual content creation.

Content Type: {content_type}

Content:
{content}

Extract the following information and provide it in JSON format:

{{
    "title": "string - main title or headline",
    "summary": "string - brief summary (2-3 sentences)",
    "key_points": ["list", "of", "main", "points", "or", "takeaways"],
    "main_themes": ["list", "of", "main", "themes", "or", "topics"],
    "tone": "string - tone of the content (professional, casual, urgent, etc.)",
    "target_audience": "string - who this content is for",
    "call_to_action": "string - any call to action or next steps (null if none)"
}}

Focus on extracting information that would be useful for creating visual content like images, infographics, or social media posts.

Provide only the JSON response:"""

        try:
            response = self.openai_client.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are an expert content analyst. Extract key information from content for visual content creation. Always respond with valid JSON only."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Clean JSON response
            if response_text.startswith('```json'):
                response_text = response_text[7:-3].strip()
            elif response_text.startswith('```'):
                response_text = response_text[3:-3].strip()
            
            import json
            return json.loads(response_text)
            
        except Exception as e:
            print(f"Error analyzing content with AI: {e}")
            return None
    
    def _create_empty_content(self) -> ArticleContent:
        """Create empty content object."""
        return ArticleContent(
            title="No Content",
            summary="No content provided",
            key_points=[],
            main_themes=[],
            tone="neutral",
            target_audience="general",
            call_to_action=None,
            raw_content=""
        )
    
    def _create_basic_content(self, content: str) -> ArticleContent:
        """Create basic content object when AI analysis fails."""
        # Extract basic information manually
        lines = content.split('\n')
        title = lines[0] if lines else "Content"
        
        # Simple summary (first 200 characters)
        summary = content[:200] + "..." if len(content) > 200 else content
        
        return ArticleContent(
            title=title,
            summary=summary,
            key_points=[],
            main_themes=[],
            tone="neutral",
            target_audience="general",
            call_to_action=None,
            raw_content=content
        )
    
    def _create_article_content(self, analysis: Dict, raw_content: str) -> ArticleContent:
        """Create ArticleContent object from AI analysis."""
        return ArticleContent(
            title=analysis.get("title", "Content"),
            summary=analysis.get("summary", ""),
            key_points=analysis.get("key_points", []),
            main_themes=analysis.get("main_themes", []),
            tone=analysis.get("tone", "neutral"),
            target_audience=analysis.get("target_audience", "general"),
            call_to_action=analysis.get("call_to_action"),
            raw_content=raw_content
        )


def process_content(content: Union[str, Dict], content_type: str = "auto") -> ArticleContent:
    """
    Convenience function for content processing.
    
    Args:
        content: Raw content (string) or structured content (dict)
        content_type: Type of content ("article", "campaign", "product", "auto")
        
    Returns:
        ArticleContent object with processed information
    """
    processor = ArticleProcessor()
    return processor.process_content(content, content_type)


def process_file(file_path: str, content_type: str = "auto") -> ArticleContent:
    """
    Convenience function for processing content from file.
    
    Args:
        file_path: Path to the content file
        content_type: Type of content ("article", "campaign", "product", "auto")
        
    Returns:
        ArticleContent object with processed information
    """
    processor = ArticleProcessor()
    return processor.process_file(file_path, content_type)
