"""
Backend service that integrates all components for the Streamlit frontend.
Handles the complete workflow from user input to image generation.
"""

import os
import json
from typing import Dict, List, Optional, Union, Tuple
from dataclasses import asdict
import logging

try:
    from .file_processor import create_file_processor
    FILE_PROCESSOR_AVAILABLE = True
except ImportError:
    FILE_PROCESSOR_AVAILABLE = False
from .article_processor import ArticleProcessor, ArticleContent
from .goal_processor import GoalProcessor, GoalRequirements
from .brand_research import research_brand
from .image_generator import ImageGenerator, ImageGenerationResult

logger = logging.getLogger(__name__)


class BackendService:
    """
    Main backend service that orchestrates the complete workflow.
    """
    
    def __init__(self):
        """Initialize the backend service with all required components."""
        self.file_processor = None
        self.article_processor = ArticleProcessor()
        self.goal_processor = GoalProcessor()
        self.image_generator = ImageGenerator()
        
        # Initialize file processor if available
        if FILE_PROCESSOR_AVAILABLE:
            try:
                self.file_processor = create_file_processor()
                logger.info("File processor initialized successfully")
            except Exception as e:
                logger.warning(f"File processor initialization failed: {e}")
        else:
            logger.warning("File processor not available - MarkItDown not installed")
    
    def process_user_request(
        self,
        brand_name: str,
        goal: str,
        content_text: Optional[str] = None,
        uploaded_file = None,
        force_refresh_brand: bool = False
    ) -> Dict:
        """
        Process a complete user request from the Streamlit frontend.
        
        Args:
            brand_name: Name of the brand
            goal: User's goal description
            content_text: Optional text content
            uploaded_file: Optional uploaded file (Streamlit file object)
            force_refresh_brand: Whether to force refresh brand research
            
        Returns:
            Dictionary with results including images, metadata, and status
        """
        logger.info(f"Processing request for brand: {brand_name}, goal: {goal}")
        
        try:
            # Step 1: Process content (text or file)
            article_content = self._process_content_input(content_text, uploaded_file)
            
            # Step 2: Research brand profile
            brand_profile = self._get_brand_profile(brand_name, force_refresh_brand)
            
            # Step 3: Process goal with article content
            goal_requirements = self._process_goal(goal, article_content)
            
            # Step 4: Generate images
            generation_result = self._generate_images(brand_profile, goal_requirements, article_content)
            
            # Step 5: Prepare response
            response = self._prepare_response(
                brand_profile, goal_requirements, article_content, generation_result
            )
            
            logger.info("Request processed successfully")
            return response
            
        except Exception as e:
            logger.error(f"Error processing request: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to process request'
            }
    
    def _process_content_input(
        self, 
        content_text: Optional[str], 
        uploaded_file = None
    ) -> ArticleContent:
        """
        Process content input (either text or uploaded file).
        
        Args:
            content_text: Optional text content
            uploaded_file: Optional uploaded file
            
        Returns:
            ArticleContent object
        """
        if uploaded_file is not None:
            if self.file_processor is None:
                raise ValueError("File processing not available - MarkItDown not installed")
            
            logger.info(f"Processing uploaded file: {uploaded_file.name}")
            
            # Convert file to markdown using MarkItDown
            markdown_content, filename = self.file_processor.process_uploaded_file(uploaded_file)
            
            # Process the markdown content with article processor
            article_content = self.article_processor.process_content(markdown_content)
            
            # Update title with filename if not detected
            if not article_content.title or article_content.title == "Content Analysis":
                article_content.title = f"Content from {filename}"
            
            logger.info(f"Successfully processed file: {filename}")
            return article_content
            
        elif content_text:
            logger.info("Processing text content")
            return self.article_processor.process_content(content_text)
            
        else:
            # Create empty content if neither provided
            logger.warning("No content provided, creating empty content")
            return ArticleContent(
                title="No Content Provided",
                summary="No content was provided for processing",
                key_points=[],
                main_themes=[],
                tone="neutral",
                target_audience="general",
                call_to_action=None,
                raw_content=""
            )
    
    def _get_brand_profile(self, brand_name: str, force_refresh: bool = False) -> Dict:
        """
        Get or research brand profile.
        
        Args:
            brand_name: Name of the brand
            force_refresh: Whether to force refresh research
            
        Returns:
            Brand profile dictionary
        """
        logger.info(f"Getting brand profile for: {brand_name}")
        
        brand_profile = research_brand(brand_name, force_refresh=force_refresh)
        
        if not brand_profile:
            raise ValueError(f"Could not obtain brand profile for {brand_name}")
        
        logger.info(f"Brand profile obtained for: {brand_name}")
        return brand_profile
    
    def _process_goal(self, goal: str, article_content: ArticleContent) -> GoalRequirements:
        """
        Process goal with article content context.
        
        Args:
            goal: Goal description
            article_content: Processed article content
            
        Returns:
            GoalRequirements object
        """
        logger.info(f"Processing goal: {goal}")
        
        goal_requirements = self.goal_processor.process_goal(goal, article_content)
        
        logger.info(f"Goal processed - {goal_requirements.image_count} images required")
        return goal_requirements
    
    def _generate_images(
        self, 
        brand_profile: Dict, 
        goal_requirements: GoalRequirements, 
        article_content: ArticleContent
    ) -> ImageGenerationResult:
        """
        Generate images using the image generator.
        
        Args:
            brand_profile: Brand profile dictionary
            goal_requirements: Goal requirements
            article_content: Article content
            
        Returns:
            ImageGenerationResult
        """
        logger.info("Starting image generation")
        
        generation_result = self.image_generator.generate_images(
            brand_profile, goal_requirements, article_content
        )
        
        logger.info(f"Generated {len(generation_result.images)} images")
        return generation_result
    
    def _prepare_response(
        self,
        brand_profile: Dict,
        goal_requirements: GoalRequirements,
        article_content: ArticleContent,
        generation_result: ImageGenerationResult
    ) -> Dict:
        """
        Prepare the response dictionary for the frontend.
        
        Args:
            brand_profile: Brand profile
            goal_requirements: Goal requirements
            article_content: Article content
            generation_result: Image generation result
            
        Returns:
            Response dictionary
        """
        return {
            'success': True,
            'brand_name': brand_profile.get('metadata', {}).get('brand_name', 'Unknown'),
            'goal': goal_requirements.goal_description,
            'content_title': article_content.title,
            'images': [
                {
                    'purpose': img.purpose,
                    'image_url': img.image_url,
                    'local_path': img.local_path,
                    'prompt': img.prompt,
                    'metadata': img.metadata
                }
                for img in generation_result.images
            ],
            'summary': generation_result.summary,
            'metadata': {
                'content_type': goal_requirements.content_type,
                'image_count': goal_requirements.image_count,
                'overall_style': goal_requirements.overall_style,
                'brand_elements': goal_requirements.brand_elements_needed,
                'article_themes': article_content.main_themes,
                'article_tone': article_content.tone,
                'target_audience': article_content.target_audience
            }
        }
    
    def get_supported_file_types(self) -> List[str]:
        """
        Get list of supported file types for upload.
        
        Returns:
            List of supported file extensions
        """
        if self.file_processor:
            return self.file_processor.get_supported_file_types()
        return []
    
    def is_file_processing_available(self) -> bool:
        """
        Check if file processing is available.
        
        Returns:
            True if file processing is available
        """
        return self.file_processor is not None


# Global service instance
_backend_service = None


def get_backend_service() -> BackendService:
    """
    Get the global backend service instance.
    
    Returns:
        BackendService instance
    """
    global _backend_service
    if _backend_service is None:
        _backend_service = BackendService()
    return _backend_service
