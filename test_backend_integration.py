#!/usr/bin/env python3
"""
Test script for the backend integration.
Tests the complete workflow from content input to image generation.
"""

import sys
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.append(str(Path(__file__).parent))

from app.backend_service import get_backend_service


def test_text_content_workflow():
    """Test the complete workflow with text content."""
    print("=" * 60)
    print("🧪 Testing Backend Integration - Text Content")
    print("=" * 60)
    
    try:
        # Get backend service
        backend = get_backend_service()
        
        # Test inputs
        brand_name = "Nike"
        goal = "Instagram Story for product launch"
        content_text = """
        Introducing the new Nike EcoRun shoes - our most sustainable running shoe yet!
        
        Made from 75% recycled materials, the EcoRun combines performance with environmental responsibility.
        
        Key Features:
        - Lightweight design for optimal performance
        - Recycled plastic upper from ocean waste
        - Bio-based foam midsole
        - Carbon-neutral manufacturing process
        
        Join the movement towards sustainable running. Every step counts for our planet.
        
        Available in stores and online starting March 15th.
        """
        
        print(f"Brand: {brand_name}")
        print(f"Goal: {goal}")
        print(f"Content length: {len(content_text)} characters")
        print()
        
        # Process the request
        print("Processing request...")
        result = backend.process_user_request(
            brand_name=brand_name,
            goal=goal,
            content_text=content_text,
            uploaded_file=None,
            force_refresh_brand=False
        )
        
        # Display results
        if result['success']:
            print("✅ Request processed successfully!")
            print()
            print("Results:")
            print(f"  Brand: {result['brand_name']}")
            print(f"  Goal: {result['goal']}")
            print(f"  Content: {result['content_title']}")
            print(f"  Images generated: {len(result['images'])}")
            print()
            
            print("Generated Images:")
            for i, image in enumerate(result['images'], 1):
                print(f"  {i}. {image['purpose']}")
                print(f"     Local path: {image['local_path']}")
                print()
            
            print("Metadata:")
            metadata = result['metadata']
            print(f"  Content type: {metadata['content_type']}")
            print(f"  Style: {metadata['overall_style']}")
            print(f"  Tone: {metadata['article_tone']}")
            print(f"  Target audience: {metadata['target_audience']}")
            print()
            
            if result['summary']:
                print("Generation Summary:")
                print(result['summary'][:500] + "..." if len(result['summary']) > 500 else result['summary'])
        
        else:
            print("❌ Request failed!")
            print(f"Error: {result.get('error', 'Unknown error')}")
            print(f"Message: {result.get('message', 'No message')}")
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()


def test_backend_service_initialization():
    """Test backend service initialization."""
    print("=" * 60)
    print("🧪 Testing Backend Service Initialization")
    print("=" * 60)
    
    try:
        backend = get_backend_service()
        
        print("Backend service components:")
        print(f"  File processor available: {backend.is_file_processing_available()}")
        print(f"  Article processor: {backend.article_processor is not None}")
        print(f"  Goal processor: {backend.goal_processor is not None}")
        print(f"  Image generator: {backend.image_generator is not None}")
        
        if backend.is_file_processing_available():
            supported_types = backend.get_supported_file_types()
            print(f"  Supported file types: {len(supported_types)} types")
            print(f"    Examples: {', '.join(supported_types[:10])}")
        
        print("✅ Backend service initialized successfully!")
        
    except Exception as e:
        print(f"❌ Backend service initialization failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run all tests."""
    print("🚀 Starting Backend Integration Tests")
    print()
    
    # Test 1: Backend service initialization
    test_backend_service_initialization()
    print()
    
    # Test 2: Text content workflow
    test_text_content_workflow()
    print()
    
    print("🏁 Tests completed!")


if __name__ == "__main__":
    main()
