#!/usr/bin/env python3
"""
Test script for the file processor module.
Tests the MarkItDown integration and file processing functionality.
"""

import os
import sys
import tempfile
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

try:
    from file_processor import create_file_processor
    print("✅ Successfully imported file_processor")
except ImportError as e:
    print(f"❌ Failed to import file_processor: {e}")
    print("Make sure MarkItDown is installed: pip install 'markitdown[all]'")
    sys.exit(1)


def test_file_processor():
    """Test the file processor with a simple text file."""
    print("\n🧪 Testing File Processor...")
    
    try:
        # Create file processor
        processor = create_file_processor()
        print("✅ File processor created successfully")
        
        # Test supported file types
        supported_types = processor.get_supported_file_types()
        print(f"✅ Supported file types: {len(supported_types)} types")
        print(f"   Examples: {', '.join(supported_types[:5])}...")
        
        # Test with a simple text file
        test_content = """# Test Document

This is a test document for the file processor.

## Features
- Text processing
- Markdown conversion
- File upload support

### Sample Content
This document contains various elements to test the conversion process.

**Bold text** and *italic text* should be preserved.

1. Numbered list item 1
2. Numbered list item 2
3. Numbered list item 3

- Bullet point 1
- Bullet point 2
- Bullet point 3

> This is a blockquote that should be preserved.

```python
# Code block example
def hello_world():
    print("Hello, World!")
```

The end.
"""
        
        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name
        
        try:
            # Test file processing
            print(f"📄 Processing test file: {temp_file_path}")
            markdown_content, filename = processor.process_file_from_path(temp_file_path)
            
            print(f"✅ Successfully processed file: {filename}")
            print(f"📊 Content length: {len(markdown_content)} characters")
            
            # Show preview
            print("\n📄 Content Preview:")
            print("-" * 50)
            preview_length = 200
            if len(markdown_content) > preview_length:
                print(markdown_content[:preview_length] + "...")
            else:
                print(markdown_content)
            print("-" * 50)
            
            # Test file type validation
            print("\n🔍 Testing file type validation...")
            
            # Test supported file
            if processor.is_supported_file("test.pdf"):
                print("✅ PDF files are supported")
            else:
                print("❌ PDF files should be supported")
            
            # Test unsupported file
            if not processor.is_supported_file("test.xyz"):
                print("✅ Unsupported file types are correctly rejected")
            else:
                print("❌ Unsupported file types should be rejected")
            
            print("\n🎉 All tests passed!")
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                print(f"🧹 Cleaned up temporary file: {temp_file_path}")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False
    
    return True


def main():
    """Main test function."""
    print("🚀 File Processor Test Suite")
    print("=" * 40)
    
    success = test_file_processor()
    
    if success:
        print("\n✅ All tests completed successfully!")
        print("The file processor is ready to use with the Streamlit frontend.")
    else:
        print("\n❌ Tests failed!")
        print("Please check the error messages above and fix any issues.")
        sys.exit(1)


if __name__ == "__main__":
    main()
