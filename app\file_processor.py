"""
File processing module using Microsoft's MarkItDown library.
Converts uploaded files to markdown format for further processing.
"""

import os
import tempfile
from typing import Optional, Tuple
from pathlib import Path
import logging

try:
    from markitdown import MarkItDown
except ImportError:
    MarkItDown = None

logger = logging.getLogger(__name__)


class FileProcessor:
    """Handles file upload processing and conversion to markdown using MarkItDown."""
    
    def __init__(self):
        """Initialize the file processor."""
        if MarkItDown is None:
            raise ImportError(
                "MarkItDown is not installed. Please install it with: pip install 'markitdown[all]'"
            )
        
        self.markitdown = MarkItDown(enable_plugins=False)
        
        # Supported file types based on MarkItDown documentation
        self.supported_extensions = {
            '.pdf', '.docx', '.pptx', '.xlsx', '.xls',
            '.txt', '.md', '.html', '.csv', '.json', '.xml',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff',
            '.mp3', '.wav', '.epub', '.zip'
        }
    
    def is_supported_file(self, filename: str) -> bool:
        """
        Check if the file type is supported by MarkItDown.
        
        Args:
            filename: Name of the file to check
            
        Returns:
            True if file type is supported, False otherwise
        """
        file_extension = Path(filename).suffix.lower()
        return file_extension in self.supported_extensions
    
    def process_uploaded_file(self, uploaded_file, save_directory: str = "uploads") -> Tuple[str, str]:
        """
        Process an uploaded file and convert it to markdown.
        
        Args:
            uploaded_file: Streamlit uploaded file object
            save_directory: Directory to save the uploaded file temporarily
            
        Returns:
            Tuple of (markdown_content, original_filename)
            
        Raises:
            ValueError: If file type is not supported
            Exception: If file processing fails
        """
        if not self.is_supported_file(uploaded_file.name):
            supported_types = ', '.join(sorted(self.supported_extensions))
            raise ValueError(
                f"File type not supported. Supported types: {supported_types}"
            )
        
        # Ensure save directory exists
        os.makedirs(save_directory, exist_ok=True)
        
        # Save uploaded file temporarily
        file_path = os.path.join(save_directory, uploaded_file.name)
        
        try:
            # Write uploaded file to disk
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            logger.info(f"Processing file: {uploaded_file.name}")
            
            # Convert to markdown using MarkItDown
            result = self.markitdown.convert(file_path)
            
            if not result or not result.text_content:
                raise Exception("Failed to extract content from file")
            
            markdown_content = result.text_content
            
            logger.info(f"Successfully converted {uploaded_file.name} to markdown")
            
            return markdown_content, uploaded_file.name
            
        except Exception as e:
            logger.error(f"Error processing file {uploaded_file.name}: {str(e)}")
            raise Exception(f"Failed to process file: {str(e)}")
        
        finally:
            # Clean up temporary file
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logger.debug(f"Cleaned up temporary file: {file_path}")
                except Exception as e:
                    logger.warning(f"Failed to clean up temporary file {file_path}: {str(e)}")
    
    def process_file_from_path(self, file_path: str) -> Tuple[str, str]:
        """
        Process a file from a given path and convert it to markdown.
        
        Args:
            file_path: Path to the file to process
            
        Returns:
            Tuple of (markdown_content, filename)
            
        Raises:
            FileNotFoundError: If file doesn't exist
            ValueError: If file type is not supported
            Exception: If file processing fails
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        filename = os.path.basename(file_path)
        
        if not self.is_supported_file(filename):
            supported_types = ', '.join(sorted(self.supported_extensions))
            raise ValueError(
                f"File type not supported. Supported types: {supported_types}"
            )
        
        try:
            logger.info(f"Processing file: {file_path}")
            
            # Convert to markdown using MarkItDown
            result = self.markitdown.convert(file_path)
            
            if not result or not result.text_content:
                raise Exception("Failed to extract content from file")
            
            markdown_content = result.text_content
            
            logger.info(f"Successfully converted {filename} to markdown")
            
            return markdown_content, filename
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {str(e)}")
            raise Exception(f"Failed to process file: {str(e)}")
    
    def get_supported_file_types(self) -> list:
        """
        Get list of supported file extensions.
        
        Returns:
            List of supported file extensions
        """
        return sorted(list(self.supported_extensions))


def create_file_processor() -> FileProcessor:
    """
    Factory function to create a FileProcessor instance.
    
    Returns:
        FileProcessor instance
        
    Raises:
        ImportError: If MarkItDown is not installed
    """
    return FileProcessor()
