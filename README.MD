# 🎨 Brand Image Generator - New Architecture

Create on-brand images with flexible goals and article content using AI-powered brand research and dynamic image generation.

## 🌟 Features

### 🔍 Goal-Agnostic Brand Research
- **Comprehensive Brand Profiling**: Researches brands without goal constraints
- **Visual Analysis**: Captures screenshots for complete brand understanding
- **AI-Powered Extraction**: Uses OpenAI's Vision API for detailed brand analysis
- **Cached Profiles**: Saves brand profiles for reuse across different goals

### 🎯 Flexible Goal Processing
- **Any Goal Definition**: Accepts any natural language goal description
- **Dynamic Requirements**: AI interprets goals and generates image requirements
- **Multiple Formats**: Supports various image types and quantities
- **Smart Adaptation**: Automatically adjusts to goal complexity

### 📄 Article-Based Content Generation
- **Content Analysis**: Processes articles, campaigns, or any text content
- **Key Point Extraction**: Identifies main themes and messages
- **Tone Analysis**: Understands content tone and target audience
- **Visual Integration**: Combines content with brand identity

### 🖼️ Dynamic Image Generation
- **On-Brand Images**: Maintains brand consistency across all outputs
- **Multiple Images**: Generates single or multiple images based on goals
- **Professional Quality**: Creates publication-ready visual content
- **Local Storage**: Saves generated images with organized file structure

## 🚀 Quick Start

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd branded-images
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**
   Create a `.env` file in the root directory:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```

### Basic Usage

#### Research a Brand
```bash
python brand_image_generator.py research "Coca-Cola"
```

#### Generate Images with Inline Content
```bash
python brand_image_generator.py generate "Nike" \
  --goal "Instagram Story for new product launch" \
  --content "Introducing our new eco-friendly running shoes made from recycled materials..."
```

#### Generate Images from Article File
```bash
python brand_image_generator.py generate "Apple" \
  --goal "LinkedIn Carousel about innovation" \
  --article "path/to/article.txt"
```

#### Force Brand Research Refresh
```bash
python brand_image_generator.py generate "Coca-Cola" \
  --goal "Product showcase banner" \
  --content "Our new sustainable packaging initiative..." \
  --refresh-brand
```

## 📁 Project Structure

```
branded-images/
├── app/
│   ├── brand_research.py      # Goal-agnostic brand profiling
│   ├── goal_processor.py      # Flexible goal interpretation
│   ├── article_processor.py   # Content analysis
│   ├── image_generator.py     # Dynamic image generation
│   ├── openai_client.py       # OpenAI API integration
│   └── ...
├── brands/                    # Cached brand profiles
├── screenshots/               # Brand research screenshots
├── generated_images/          # Generated image outputs
├── generation_summaries/      # Generation reports
├── brand_image_generator.py   # New main entry point
├── test_new_architecture.py   # Architecture validation
└── NEW_ARCHITECTURE_DESIGN.md # Detailed architecture docs
```

## 🎯 Example Goals

The system accepts any natural language goal description:

- **Social Media**: "LinkedIn Carousel about sustainability", "Instagram Story for product launch"
- **Web Content**: "Hero banner for website homepage", "Blog article header image"
- **Marketing**: "Email newsletter header", "Product showcase for e-commerce"
- **Presentations**: "Title slide for investor presentation", "Infographic about company values"
- **Campaigns**: "Brand awareness banner", "Event promotion poster"

## 📊 Output Examples

### Generated Images
- **LinkedIn Carousel**: 5-8 images (title + content slides + CTA)
- **Instagram Story**: 3-5 vertical images
- **Hero Banner**: 1 landscape image
- **Product Showcase**: 3-6 product-focused images

### File Organization
```
generated_images/
├── coca_cola/
│   ├── linkedin_carousel_sustainability/
│   │   ├── 01_title_slide.png
│   │   ├── 02_content_slide_1.png
│   │   └── 03_cta_slide.png
│   └── instagram_story_product/
│       ├── 01_hero_image.png
│       └── 02_product_showcase.png
```

## 🧪 Testing

Run the architecture validation test:

```bash
python test_new_architecture.py
```

This tests:
- ✅ Brand research functionality
- ✅ Goal processing with various examples
- ✅ Article content analysis
- ✅ Image generation pipeline (optional - uses API credits)

## 🔄 Migration from Old System

The new architecture coexists with the existing system:

### Old System (Still Available)
```bash
python brand_content_generator.py "Nike" --mode auto --goal linkedin_carousel
```

### New System (Recommended)
```bash
python brand_image_generator.py generate "Nike" \
  --goal "LinkedIn Carousel about innovation" \
  --content "Your article content here..."
```

## 🛠️ Advanced Usage

### Custom Content Types
```bash
# Campaign content
python brand_image_generator.py generate "Starbucks" \
  --goal "Holiday campaign social media" \
  --content "Celebrate the season with our new holiday drinks..."

# Product launch
python brand_image_generator.py generate "Tesla" \
  --goal "Product announcement banner" \
  --content "Introducing the new Model Y with enhanced autopilot..."
```

### Batch Processing
Create a script to process multiple goals:

```python
from app.brand_research import research_brand
from app.goal_processor import process_goal
from app.article_processor import process_content
from app.image_generator import generate_images

# Research brand once
brand_profile = research_brand("Nike")

# Process multiple goals
goals = [
    "Instagram Story for product launch",
    "LinkedIn post about sustainability",
    "Email header for newsletter"
]

for goal in goals:
    article_content = process_content("Your content here...")
    goal_requirements = process_goal(goal, article_content)
    result = generate_images(brand_profile, goal_requirements, article_content)
```

## 📋 Requirements

- Python 3.8+
- OpenAI API key
- Internet connection for brand research
- Chrome/Chromium browser (for screenshot capture)

## 🔧 Dependencies

Key dependencies (see `requirements.txt` for full list):
- `openai` - For AI analysis and image generation
- `requests` - For web requests
- `beautifulsoup4` - For HTML parsing
- `googlesearch-python` - For Google search
- `selenium` - For web automation and screenshots

## 🚨 Troubleshooting

### Common Issues

1. **Chrome/Chromium not found**: Install Chrome or Chromium browser
2. **API rate limits**: The system includes delays between requests
3. **No brand results**: Try different brand names or check internet connection
4. **Goal processing fails**: Ensure goal descriptions are clear and specific

### Error Handling

The system includes comprehensive error handling for:
- Network connectivity issues
- API rate limits and failures
- Invalid brand names or goals
- Screenshot capture failures
- Content processing errors

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

[Add your license information here]

---

## 🔗 Related Files

- **[NEW_ARCHITECTURE_DESIGN.md](NEW_ARCHITECTURE_DESIGN.md)**: Detailed architecture documentation
- **[IMPLEMENTATION_SUMMARY.md](IMPLEMENTATION_SUMMARY.md)**: Implementation progress and changes
- **[test_new_architecture.py](test_new_architecture.py)**: Comprehensive testing script
